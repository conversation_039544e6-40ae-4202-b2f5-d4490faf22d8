package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	gobase "github.com/real-rm/gobase"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/goprocess"
	"github.com/real-rm/goresodownload"
	"github.com/real-rm/gowatch"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Graceful shutdown timeout constants
const (
	// GracefulShutdownTimeoutMinutes defines how long to wait for all tasks to complete during shutdown
	// Based on production data with 5 concurrent downloads per prop:
	// - Single prop: 50 images ÷ 5 concurrent = 10 batches × 0.52s = 5.2s per prop
	// - 70 props with system limits: ~10-20 props concurrent = 5.2s × 4 rounds = ~21s theoretical
	// - With database updates, queue operations, and system overhead: 21s × 10 = 3.5min practical
	// - Adding safety buffer for peak load and network issues: 3.5min × 4 = 15min
	// ca2 batchSize 70, 15min is not enough
	GracefulShutdownTimeoutMinutes = 20 // 20 minutes should be more than sufficient

	// GracefulShutdownTimeout is the actual timeout duration
	GracefulShutdownTimeout = GracefulShutdownTimeoutMinutes * time.Minute

	// GracefulShutdownCheckInterval defines how often to check task completion status
	GracefulShutdownCheckInterval = 2 * time.Second

	// GracefulShutdownDetailedLogInterval defines how often to log detailed status (to reduce log spam)
	GracefulShutdownDetailedLogInterval = 10 * time.Second
)

// Watch operation constants
const (
	// Retry limits for different operations
	MaxWatchRetries = 5 // Maximum retries for watch recovery
	MaxTokenRetries = 3 // Maximum retries for token operations

	// Watch configuration
	HighWaterMark     = 20  // High water mark for watch operations
	UpdateTokenTimerS = 120 // Token update timer in seconds

)

// UpdateProcessStatus updates the process status
func UpdateProcessStatus(startTs time.Time) error {
	golog.Info("UpdateProcessStatus", "startTs", startTs)
	opts := goprocess.UpdateProcessStatusOptions{
		Status:   goprocess.RunningNormal,
		StartTs:  &startTs,
		ErrorMsg: nil,
		Stats:    nil,
	}
	return gProcessMonitor.UpdateProcessStatus(opts)
}

// OnTokenUpdate handles token updates
func OnTokenUpdate(tokenOpt gowatch.TokenUpdateOptions) error {
	// Update stats first with a separate lock
	statMu.Lock()
	golog.Info("onTokenUpdate", "token", tokenOpt.Token, "gStat", gStat, "from:", lastTokenPrintTs)
	lastTokenPrintTs = time.Now()
	gStat = make(map[string]int)
	statMu.Unlock()

	// Update token with minimal lock scope
	tokenMu.Lock()
	updateFields := gowatch.UpdateFields{
		Token:          tokenOpt.Token,
		TokenClusterTs: tokenOpt.TokenClusterTs,
		TokenChangeTs:  tokenOpt.TokenChangeTs,
		Status:         "running",
		ResumeMt:       tokenOpt.ResumeMt,
	}
	tokenMu.Unlock()

	return gUpdateSysData(updateFields)
}

// OnChange handles property changes
func OnChange(changeDoc bson.M, coll *gomongo.MongoCollection) error {
	changeType := changeDoc["operationType"].(string)
	statMu.Lock()
	if _, ok := gStat[changeType]; !ok {
		gStat[changeType] = 0
	}
	gStat[changeType] = gStat[changeType] + 1
	statMu.Unlock()

	if gWatchedObject == nil {
		golog.Error("gWatchedObject is nil")
		return fmt.Errorf("gWatchedObject is nil")
	}
	return gowatch.ProcessChangedObject(gowatch.ProcessChangedObjectOptions{
		ChangedObj:  changeDoc,
		WatchedColl: coll,
		DeleteOneFn: func(id interface{}) error {
			return ProcessDelete(changeDoc, id)
		},
		InsertOneFn: func(prop bson.M) error {
			return ProcessInsert(changeDoc, prop)
		},
		ReplaceOneFn: func(prop bson.M) error {
			return ProcessReplace(changeDoc, prop)
		},
		UpdateOneFn: func(prop bson.M) error {
			return ProcessUpdate(changeDoc, prop)
		},
		WatchedStream: gWatchedObject,
	})
}

// WatchProp sets up the watch on properties collection
func WatchProp(ctx context.Context, cancel context.CancelFunc, token *bson.M, tokenClusterTs time.Time, resumeMt ...time.Time) error {
	return watchPropWithRetry(ctx, cancel, token, tokenClusterTs, 0, resumeMt...)
}

// watchPropWithRetry sets up the watch on properties collection with retry limit
func watchPropWithRetry(ctx context.Context, cancel context.CancelFunc, token *bson.M, tokenClusterTs time.Time, retryCount int, resumeMt ...time.Time) error {
	// Check retry limit
	if retryCount >= MaxWatchRetries {
		err := fmt.Errorf("max retries (%d) reached for watch recovery", MaxWatchRetries)
		golog.Error("Max retries reached for watch recovery", "retryCount", retryCount, "maxRetries", MaxWatchRetries)
		GracefulExit(err)
		return err
	}

	if retryCount > 0 {
		golog.Info("Retrying watch setup", "retryCount", retryCount, "maxRetries", MaxWatchRetries)
	}

	// Use background context for watch operation
	var startTs time.Time
	if len(resumeMt) > 0 && !resumeMt[0].IsZero() {
		startTs = resumeMt[0]
	} else {
		startTs = time.Now()
		if !tokenClusterTs.IsZero() {
			golog.Debug("tokenClusterTs is not zero", "tokenClusterTs", tokenClusterTs)
			startTs = tokenClusterTs
		}
	}

	// Get the appropriate collection based on board type
	watchedColl := gomongo.Coll("rni", goresodownload.BoardWatchTable[gBoardType])
	golog.Debug("Watching collection:", "collection", goresodownload.BoardWatchTable[gBoardType])

	query := bson.M{"mt": bson.M{"$gte": startTs}}
	golog.Debug("Watch query:", "query", query)

	// Function to create watch options
	createWatchOptions := func(token *bson.M) gowatch.WatchOptions {
		return gowatch.WatchOptions{
			WatchedColl:           watchedColl,
			OnChange:              OnChange,
			OnTokenUpdate:         OnTokenUpdate,
			QueryWhenInvalidToken: query,
			SavedToken:            token,
			HighWaterMark:         HighWaterMark,
			OnError: func(err error) {
				golog.Error("watchProp error", "error", err.Error(), "retryCount", retryCount)
				// Check if it's a ChangeStreamHistoryLost error
				if strings.Contains(err.Error(), "ChangeStreamHistoryLost") ||
					strings.Contains(err.Error(), "Resume of change stream was not possible") {
					golog.Warn("Change stream history lost, attempting to get new token", "retryCount", retryCount)
					// Check retry limit before attempting recovery
					if retryCount >= MaxWatchRetries {
						golog.Error("Max retries reached in OnError callback", "retryCount", retryCount, "maxRetries", MaxWatchRetries)
						GracefulExit(fmt.Errorf("max retries reached in OnError: %w", err))
						return
					}
					// Get a new token and retry with incremented count
					if newToken, newTokenClusterTs, err := gowatch.GetToken(watchedColl); err == nil {
						tokenOpt := gowatch.TokenUpdateOptions{
							Token:          newToken,
							TokenClusterTs: newTokenClusterTs,
						}
						if err := OnTokenUpdate(tokenOpt); err == nil {
							if err := watchPropWithRetry(ctx, cancel, newToken, newTokenClusterTs, retryCount+1); err == nil {
								return
							}
						}
					}
				}
				GracefulExit(err)
			},
			UpdateProcessStatusFn: func() error {
				return UpdateProcessStatus(time.Now())
			},
			UpdateTokenTimerS: UpdateTokenTimerS,
			Context:           ctx,
			Cancel:            cancel,
		}
	}

	// Try to create watch with current token
	opt := createWatchOptions(token)
	watchObj, err := gowatch.WatchTarget(opt)
	if err != nil {
		// If error is ChangeStreamHistoryLost, try to get a new token and retry
		if strings.Contains(err.Error(), "ChangeStreamHistoryLost") ||
			strings.Contains(err.Error(), "Resume of change stream was not possible") {
			golog.Warn("Change stream history lost during creation, getting new token")
			if newToken, newTokenClusterTs, err := gowatch.GetToken(watchedColl); err == nil {
				tokenOpt := gowatch.TokenUpdateOptions{
					Token:          newToken,
					TokenClusterTs: newTokenClusterTs,
				}
				if err := OnTokenUpdate(tokenOpt); err == nil {
					// Retry with new token
					opt = createWatchOptions(newToken)
					watchObj, err = gowatch.WatchTarget(opt)
					if err == nil {
						gWatchedObject = watchObj
						golog.Debug("Watch started successfully with new token")
						return nil
					}
				}
			}
		}
		golog.Error("watchProp error", "error", err)
		GracefulExit(err)
		return err
	}

	gWatchedObject = watchObj
	golog.Debug("Watch started successfully")
	return nil
}

// GetTokenAndWatch gets a new token and starts watching
func GetTokenAndWatch(ctx context.Context, cancel context.CancelFunc) error {
	return getTokenAndWatchWithRetry(ctx, cancel, 0)
}

// getTokenAndWatchWithRetry gets a new token and starts watching with retry limit
func getTokenAndWatchWithRetry(ctx context.Context, cancel context.CancelFunc, retryCount int) error {
	if retryCount >= MaxTokenRetries {
		err := fmt.Errorf("max retries (%d) reached for token and watch", MaxTokenRetries)
		golog.Error("Max retries reached for GetTokenAndWatch", "retryCount", retryCount, "maxRetries", MaxTokenRetries)
		GracefulExit(err)
		return err
	}

	if retryCount > 0 {
		golog.Info("Retrying GetTokenAndWatch", "retryCount", retryCount, "maxRetries", MaxTokenRetries)
	}

	watchedColl := gomongo.Coll("rni", goresodownload.BoardWatchTable[gBoardType])

	token, tokenClusterTs, err := gowatch.GetToken(watchedColl)
	if err != nil {
		GracefulExit(err)
		return err
	}

	tokenOpt := gowatch.TokenUpdateOptions{
		Token:          token,
		TokenClusterTs: tokenClusterTs,
	}
	if err := OnTokenUpdate(tokenOpt); err != nil {
		GracefulExit(err)
		return err
	}
	golog.Debug("OnTokenUpdate", "token", token)
	return WatchProp(ctx, cancel, token, tokenClusterTs)
}

// handleTokenAndWatch handles token retrieval and watching logic
func handleTokenAndWatch(watchCtx context.Context, watchCancel context.CancelFunc, sysData bson.M, startTs time.Time) error {
	return handleTokenAndWatchWithRetry(watchCtx, watchCancel, sysData, startTs, 0)
}

// handleTokenAndWatchWithRetry handles token retrieval and watching logic with retry limit
func handleTokenAndWatchWithRetry(watchCtx context.Context, watchCancel context.CancelFunc, sysData bson.M, startTs time.Time, retryCount int) error {
	if retryCount >= MaxTokenRetries {
		err := fmt.Errorf("max retries (%d) reached for handleTokenAndWatch", MaxTokenRetries)
		golog.Error("Max retries reached for handleTokenAndWatch", "retryCount", retryCount, "maxRetries", MaxTokenRetries)
		GracefulExit(err)
		return err
	}

	if retryCount > 0 {
		golog.Info("Retrying handleTokenAndWatch", "retryCount", retryCount, "maxRetries", MaxTokenRetries)
	}
	token := sysData["token"]

	var tokenClusterTs primitive.DateTime
	if v, ok := sysData["tokenClusterTs"].(primitive.DateTime); ok {
		tokenClusterTs = v
	}

	var resumeMt time.Time
	if v, ok := sysData["resumeMt"].(primitive.DateTime); ok {
		resumeMt = v.Time()
	}

	// Enhanced token validation
	if token == nil {
		golog.Info("No token found, starting fresh watch")
		return getTokenAndWatchWithRetry(watchCtx, watchCancel, retryCount+1)
	}

	// Parse and validate existing token
	var parsedToken bson.M
	if err := json.Unmarshal([]byte(token.(string)), &parsedToken); err != nil {
		golog.Info("Invalid token format, starting fresh", "token", token)
		return getTokenAndWatchWithRetry(watchCtx, watchCancel, retryCount+1)
	}

	// Check if token is too old (more than 12 hours)
	tokenAge := time.Since(tokenClusterTs.Time())
	if tokenAge > HALF_DAY {
		golog.Warn("Token is too old, starting fresh watch",
			"tokenAge", tokenAge,
			"tokenClusterTs", tokenClusterTs.Time(),
			"maxAge", HALF_DAY)
		return getTokenAndWatchWithRetry(watchCtx, watchCancel, retryCount+1)
	}

	// If resumeMt is set, use it as the start point
	if !resumeMt.IsZero() {
		golog.Info("Using resumeMt as start point", "resumeMt", resumeMt)
		tokenOpt := gowatch.TokenUpdateOptions{
			Token:          &parsedToken,
			TokenClusterTs: tokenClusterTs.Time(),
			ResumeMt:       resumeMt,
		}
		if err := OnTokenUpdate(tokenOpt); err != nil {
			golog.Error("Failed to update token with resumeMt", "error", err)
			return getTokenAndWatchWithRetry(watchCtx, watchCancel, retryCount+1)
		}
		return WatchProp(watchCtx, watchCancel, &parsedToken, tokenClusterTs.Time(), resumeMt)
	}

	// Use existing token with tokenClusterTs
	golog.Info("Continuing watch from existing token",
		"tokenClusterTs", tokenClusterTs.Time(),
		"tokenAge", tokenAge)
	tokenOpt := gowatch.TokenUpdateOptions{
		Token:          &parsedToken,
		TokenClusterTs: tokenClusterTs.Time(),
	}
	if err := OnTokenUpdate(tokenOpt); err != nil {
		golog.Error("Failed to update token", "error", err)
		return getTokenAndWatchWithRetry(watchCtx, watchCancel, retryCount+1)
	}

	// Try to resume with existing token
	err := WatchProp(watchCtx, watchCancel, &parsedToken, tokenClusterTs.Time())
	if err != nil {
		if strings.Contains(err.Error(), "ChangeStreamHistoryLost") ||
			strings.Contains(err.Error(), "Resume of change stream was not possible") {
			golog.Warn("Change stream history lost, starting fresh watch", "error", err)
			return getTokenAndWatchWithRetry(watchCtx, watchCancel, retryCount+1)
		}
		return err
	}

	return nil
}

// hasMediaUpdate checks if the changeDoc contains relevant media field updates
func hasMediaUpdate(changeDoc bson.M, operationType string) bool {
	if operationType == "insert" || operationType == "replace" {
		return true
	}

	updateDesc, ok := changeDoc["updateDescription"].(bson.M)
	if !ok {
		golog.Warn("hasMediaUpdate no updateDescription", "updateDesc", updateDesc)
		return false
	}

	updatedFields, ok := updateDesc["updatedFields"].(bson.M)
	if !ok {
		golog.Warn("hasMediaUpdate no updatedFields", "updatedFields", updatedFields)
		return false
	}

	// Check for Media field updates (CAR, DDF, BRE, EDM)
	if gBoardType != "TRB" {
		_, hasMedia := updatedFields["Media"]
		return hasMedia
	}

	// Check for phoUrls field updates (TRB)
	_, hasMedia := updatedFields["media"]

	return hasMedia
}

// ProcessInsert handles property insertion
func ProcessInsert(changeDoc bson.M, prop bson.M) error {
	if prop["_id"] == nil {
		golog.Error("invalid prop: no id")
		return fmt.Errorf("no id")
	}
	// Check if this change contains relevant media updates
	changeDocType, ok := changeDoc["operationType"].(string)
	if !ok {
		golog.Error("invalid changeDoc: no operationType")
		return fmt.Errorf("no operationType")
	}
	if !hasMediaUpdate(changeDoc, changeDocType) {
		golog.Info("No relevant media updates, skipping", "propId", prop["_id"])
		return nil
	}
	speedMeter.Check("needAnalyze", 1)

	// Get priority for queue
	priority := getPriority(prop)
	if priority < 0 {
		golog.Error("invalid priority value", "priority", priority, "propId", prop["_id"])
		return fmt.Errorf("invalid priority value: %d", priority)
	}

	// Add to queue for processing
	propID := prop["_id"].(string)
	if err := downloadQueue.AddToQueue(propID, priority, gBoardType); err != nil {
		golog.Error("Failed to add to download queue", "propId", prop["_id"], "error", err)
		return fmt.Errorf("failed to add to download queue: %w", err)
	}

	golog.Debug("Successfully added to download queue", "propId", prop["_id"], "priority", priority)
	speedMeter.Check("addedToQueue", 1)
	return nil
}

// ProcessReplace handles property replacement
func ProcessReplace(changeDoc bson.M, prop bson.M) error {
	return ProcessInsert(changeDoc, prop)
}

// ProcessUpdate handles property updates
func ProcessUpdate(changeDoc bson.M, prop bson.M) error {
	// golog.Info("ProcessUpdate", "prop", prop["_id"])
	return ProcessInsert(changeDoc, prop)
}

// ProcessDelete handles property deletion
func ProcessDelete(changeDoc bson.M, id interface{}) error {
	golog.Warn("Process delete", "id", id, "changeDoc", changeDoc)
	return nil
}

// GetSignalChan returns a channel that informs about pressing Ctrl+C
func GetSignalChan() chan os.Signal {
	signalChannel := make(chan os.Signal, 1)
	signal.Notify(signalChannel,
		syscall.SIGHUP,  // Terminal hangup
		syscall.SIGINT,  // Interrupt (Ctrl+C)
		syscall.SIGTERM, // Termination request
		syscall.SIGQUIT) // Quit (Ctrl+\)
	return signalChannel
}

// GracefulExit handles graceful shutdown
func GracefulExit(err error) {
	shutdownStartTime := time.Now()

	if err != nil {
		golog.Error("gracefulExit error", "err", err.Error())
	}

	params := map[string]interface{}{
		"watchedObject": gWatchedObject,
		"exitFn":        gobase.Exit,
	}
	if err != nil {
		params["error"] = err.Error()
	}

	// Wait for all ongoing processing to complete
	// Critical: Do not stop downloader before waiting - let it continue processing all images
	golog.Info("Starting graceful shutdown - waiting for ongoing processing to complete")
	count, props := getProcessingStatus()

	if count == 0 {
		golog.Info("No active tasks at shutdown start - proceeding with immediate shutdown")
	} else {
		golog.Info("Active tasks found at shutdown start", "activeCount", count, "tasks", props)
	}

	// Wait for all prop image processing to complete
	waitForProcessingComplete()

	// Only stop downloader after all prop images are fully processed
	if downloader != nil {
		golog.Info("All prop processing completed - now stopping downloader")
		downloader.Stop()
		golog.Info("Downloader stopped successfully")
	}

	if gWatchedObject != nil {
		if err := gWatchedObject.FinishAndUpdateSysdata(gUpdateSysData); err != nil {
			golog.Error("FinishAndUpdateSysdata error", "err", err.Error())
		}
	}

	errMsg := func() string {
		if err != nil {
			return err.Error()
		}
		return ""
	}()

	gProcessMonitor.UpdateStatusWhenAllFinish(errMsg, func() {
		shutdownDuration := time.Since(shutdownStartTime)
		golog.Info("UpdateStatusWhenAllFinish", "err", errMsg, "shutdownDurationMin", shutdownDuration.Minutes())
	})
}

// waitForProcessingComplete waits for all ongoing processing to complete
func waitForProcessingComplete() {
	golog.Info("Starting graceful shutdown - waiting for all tasks to fully complete")

	// Use configurable timeout to ensure sufficient time for all steps to complete
	timeout := time.After(GracefulShutdownTimeout)
	ticker := time.NewTicker(GracefulShutdownCheckInterval)
	defer ticker.Stop()

	// Record start time for duration tracking
	startWaitTime := time.Now()
	lastLogTime := startWaitTime

	for {
		select {
		case <-timeout:
			golog.Error("Timeout waiting for processing to complete - forcing shutdown",
				"timeoutMinutes", GracefulShutdownTimeoutMinutes)
			count, props := getProcessingStatus()
			golog.Error("Incomplete tasks at forced shutdown",
				"activeCount", count,
				"tasks", props,
				"waitDurationMin", time.Since(startWaitTime).Minutes())

			// Log detailed status for each incomplete task
			for i, task := range props {
				golog.Error("Incomplete task detail", "taskIndex", i+1, "task", task)
			}
			return
		case <-ticker.C:
			// Check if all tasks are fully completed (including all image downloads)
			fullyComplete := isAllTasksFullyComplete()

			if fullyComplete {
				golog.Info("All prop tasks and image downloads fully completed - graceful shutdown ready",
					"waitDurationMin", time.Since(startWaitTime).Minutes())
				return
			}

			count, props := getProcessingStatus()

			// Log detailed status periodically to avoid log spam
			now := time.Now()
			if now.Sub(lastLogTime) >= GracefulShutdownDetailedLogInterval {
				if count > 0 {
					golog.Info("Waiting for all prop image processing to complete",
						"activeCount", count,
						"tasks", props,
						"waitDurationMin", time.Since(startWaitTime).Minutes())
				} else {
					golog.Info("No active tasks remaining",
						"waitDurationMin", time.Since(startWaitTime).Minutes())
				}
				lastLogTime = now

				// Log downloader status if available
				if downloader != nil && count > 0 {
					golog.Info("Downloader still active - waiting for all image downloads to complete")
				}
			} else {
				// Simplified debug logging
				if count > 0 {
					golog.Debug("Still waiting for prop and image processing completion",
						"activeCount", count,
						"waitDurationMin", time.Since(startWaitTime).Minutes())
				}
			}
		}
	}
}
