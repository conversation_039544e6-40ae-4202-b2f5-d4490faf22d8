/*
###
Description:    Batch add properties to download queue with flexible query conditions

Usage:

	# Process all documents without phoLH:
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -dryrun"

	# Process specific IDs (bracket format):
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -id=[TRBC12248877,TRBC12248878,TRBC12248879] -dryrun"

	# Process documents with custom query (documents that have docLH field and it's not null):
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -query='{\"docLH\":{\"$ne\":null}}' -dryrun"

	# Process documents with simplified query format (minimal quotes):
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -query='{docLH:{$ne:null}}' -dryrun"

	# Process documents with ultra-simplified format (no outer braces needed, but quotes required for shell):
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -query='docLH:{$ne:null}' -dryrun"

	# Process documents with multiple conditions:
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -query='docLH:{$ne:null},phoLH:{$eq:null}' -dryrun"

Create date:    2025-07-21
Author:         Maggie
Run frequency:  As needed
###
*/
package main

import (
	"context"
	"flag"
	"fmt"
	"strings"
	"time"

	goconfig "github.com/real-rm/goconfig"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/goresodownload"
	"github.com/real-rm/gospeedmeter"
	gostreaming "github.com/real-rm/gostreaming"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	dryrunFlag = flag.Bool("dryrun", false, "Dry run mode - only log operations without executing them")
	boardFlag  = flag.String("board", "TRB", "Board name for processing")
	idFlag     = flag.String("id", "", "Property IDs to add to queue (optional, uses priority 50000). Format: id1,id2,id3 or [id1,id2,id3]")
	queryFlag  = flag.String("query", "", "Custom query conditions in JSON format (e.g., 'docLH:{$ne:null}' or '{docLH:{$ne:null}}' or '{\"docLH\":{\"$ne\":null}}') - quotes required to protect shell special characters")
	speedMeter *gospeedmeter.SpeedMeter
	startTime  = time.Now()
	queue      *goresodownload.ResourceDownloadQueue
)

// setting up logging, and establishing MongoDB connection.
func init() {
	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		golog.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logging first
	if err := golog.InitLog(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}

	// Initialize MongoDB last (after config is loaded)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Initialize speed meter
	speedMeter = gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})
}

// preprocessQuery converts simplified query format to standard JSON
// Supports: {docLH:{$ne:null}} -> {"docLH":{"$ne":null}}
// Also supports: docLH:{$ne:null} -> {"docLH":{"$ne":null}}
func preprocessQuery(query string) string {
	// If it already looks like proper JSON (contains quotes), return as-is
	if strings.Contains(query, `"`) {
		return query
	}

	// Handle queries without outer braces (e.g., docLH:{$ne:null})
	result := strings.TrimSpace(query)
	if !strings.HasPrefix(result, "{") {
		result = "{" + result + "}"
	}

	// Simple replacements for common patterns
	// Replace common field names
	result = strings.ReplaceAll(result, "docLH:", `"docLH":`)
	result = strings.ReplaceAll(result, "phoLH:", `"phoLH":`)
	result = strings.ReplaceAll(result, "_id:", `"_id":`)

	// Replace MongoDB operators
	result = strings.ReplaceAll(result, "$ne:", `"$ne":`)
	result = strings.ReplaceAll(result, "$eq:", `"$eq":`)
	result = strings.ReplaceAll(result, "$in:", `"$in":`)
	result = strings.ReplaceAll(result, "$size:", `"$size":`)
	result = strings.ReplaceAll(result, "$exists:", `"$exists":`)
	result = strings.ReplaceAll(result, "$gt:", `"$gt":`)
	result = strings.ReplaceAll(result, "$lt:", `"$lt":`)
	result = strings.ReplaceAll(result, "$gte:", `"$gte":`)
	result = strings.ReplaceAll(result, "$lte:", `"$lte":`)
	result = strings.ReplaceAll(result, "$or:", `"$or":`)
	result = strings.ReplaceAll(result, "$and:", `"$and":`)

	// Handle special values - null should remain as null (not quoted)
	// No need to change :null to :"null" because null is a JSON literal

	return result
}

func processProperties(ctx context.Context) error {
	// Parse command line flags
	flag.Parse()
	golog.Info("Starting AddToQueue batch processing", "dryrun", *dryrunFlag, "board", *boardFlag, "id", *idFlag, "query", *queryFlag)

	// Validate board flag
	collectionName, exists := goresodownload.BoardMergedTable[*boardFlag]
	if !exists {
		return fmt.Errorf("invalid board: %s. Valid boards: CAR, DDF, BRE, EDM, TRB", *boardFlag)
	}

	// Initialize ResourceDownloadQueue
	golog.Info("Initializing ResourceDownloadQueue")
	queueCol := gomongo.Coll("rni", "reso_photo_download_queue")
	if queueCol == nil {
		return fmt.Errorf("failed to get queue collection: reso_photo_download_queue")
	}

	var err error
	queue, err = goresodownload.NewResourceDownloadQueue(queueCol)
	if err != nil {
		golog.Error("Failed to initialize ResourceDownloadQueue", "error", err)
		return fmt.Errorf("failed to initialize ResourceDownloadQueue: %v", err)
	}
	golog.Info("ResourceDownloadQueue initialized successfully")

	// Get collection using BoardMergedTable
	coll := gomongo.Coll("rni", collectionName)
	if coll == nil {
		return fmt.Errorf("failed to get merged collection: %s", collectionName)
	}
	golog.Info("Processing collection", "board", *boardFlag, "collection", collectionName, "dryrun", *dryrunFlag)

	// Build query based on whether specific IDs or custom query are provided
	var query bson.M
	if *idFlag != "" {
		// Parse IDs - expected format: "[id1,id2,id3]" (also supports legacy comma-separated format for compatibility)
		idString := strings.TrimSpace(*idFlag)

		// Remove brackets if present
		if strings.HasPrefix(idString, "[") && strings.HasSuffix(idString, "]") {
			idString = strings.TrimPrefix(idString, "[")
			idString = strings.TrimSuffix(idString, "]")
		}

		// Split by comma and trim whitespace
		idList := strings.Split(idString, ",")
		for i, id := range idList {
			idList[i] = strings.TrimSpace(id)
		}

		// Remove empty strings
		var cleanIdList []string
		for _, id := range idList {
			if id != "" {
				cleanIdList = append(cleanIdList, id)
			}
		}

		if len(cleanIdList) == 1 {
			// Single ID
			query = bson.M{"_id": cleanIdList[0]}
			golog.Info("Processing specific ID", "id", cleanIdList[0])
		} else {
			// Multiple IDs
			query = bson.M{"_id": bson.M{"$in": cleanIdList}}
			golog.Info("Processing specific IDs", "ids", cleanIdList, "count", len(cleanIdList))
		}
	} else if *queryFlag != "" {
		// Parse custom query from JSON (support both standard JSON and simplified format)
		queryStr := preprocessQuery(*queryFlag)
		err := bson.UnmarshalExtJSON([]byte(queryStr), true, &query)
		if err != nil {
			return fmt.Errorf("failed to parse custom query JSON: %v", err)
		}
		golog.Info("Processing with custom query", "query", query)
	} else {
		// If no ID or custom query provided, query documents without phoLH field or with empty phoLH
		query = bson.M{
			"$or": []bson.M{
				{"phoLH": bson.M{"$eq": nil}},
				{"phoLH": bson.M{"$size": 0}}, // empty array
			},
		}
		golog.Info("Processing documents without phoLH")
	}

	// Get cursor
	golog.Info("Executing query", "query", query)
	cursor, err := coll.Find(ctx, query)
	if err != nil {
		golog.Error("Failed to execute query", "error", err)
		return fmt.Errorf("failed to execute query: %v", err)
	}
	golog.Info("Query executed successfully, starting streaming")

	opts := gostreaming.StreamingOptions{
		Stream: cursor,
		Process: func(item interface{}) error {
			return processItem(ctx, item)
		},
		End: func(err error) {
			duration := time.Since(startTime)
			fmt.Println("Total process time:", duration)
			// Check if error is not nil and has actual content
			if err != nil && err.Error() != "" {
				golog.Error("Stream ended with error", "error", err, "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			} else {
				golog.Info("Stream completed successfully", "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			}
		},
		Error: func(err error) {
			golog.Error("Processing error", "error", err)
		},
		High:    10,
		Verbose: 2,
	}

	golog.Info("Starting gostreaming.Streaming")
	err = gostreaming.Streaming(ctx, &opts)
	if err != nil {
		golog.Error("Failed to stream data", "error", err)
		return err
	}
	golog.Info("Streaming completed successfully")
	return nil
}

// processItem processes a single document to add to download queue
func processItem(ctx context.Context, item interface{}) error {
	_ = ctx // Context not used in this function but required by gostreaming interface
	golog.Debug("Processing item started")

	// Track processing speed
	speedMeter.Check("processed", 1)

	// Convert item to bson.M (handle both bson.M and bson.D types)
	var doc bson.M
	switch v := item.(type) {
	case bson.M:
		doc = v
	case bson.D:
		// Convert bson.D to bson.M using marshal/unmarshal
		data, err := bson.Marshal(v)
		if err != nil {
			golog.Error("Failed to marshal bson.D", "error", err, "item", item)
			speedMeter.Check("errors", 1)
			return fmt.Errorf("failed to marshal bson.D: %v", err)
		}
		if err := bson.Unmarshal(data, &doc); err != nil {
			golog.Error("Failed to unmarshal to bson.M", "error", err, "item", item)
			speedMeter.Check("errors", 1)
			return fmt.Errorf("failed to unmarshal to bson.M: %v", err)
		}
	default:
		golog.Error("Unsupported document type", "type", fmt.Sprintf("%T", item), "item", item)
		speedMeter.Check("errors", 1)
		return fmt.Errorf("unsupported document type: %T", item)
	}

	golog.Debug("Item converted to bson.M successfully")

	// Extract required fields
	id, ok := doc["_id"].(string)
	if !ok {
		// Try ObjectID
		if objID, ok := doc["_id"].(primitive.ObjectID); ok {
			golog.Info("ObjectID found", "objID", objID)
			id = objID.Hex()
		} else {
			golog.Error("Failed to extract _id", "doc", doc)
			speedMeter.Check("idErrors", 1)
			return fmt.Errorf("failed to extract _id")
		}
	}

	// If processing specific IDs (idFlag provided) or custom query (queryFlag provided), skip phoLH check and process directly
	// Otherwise, check if document has phoLH - if yes, skip it
	if *idFlag == "" && *queryFlag == "" {
		phoLH, hasPhoLH := doc["phoLH"]
		if hasPhoLH && phoLH != nil {
			// Helper function to handle array length check
			handleArraySkip := func(arrayLen int) error {
				if arrayLen > 0 {
					golog.Debug("Skipping document: has non-empty phoLH array", "_id", id)
					speedMeter.Check("skipped_has_phoLH", 1)
				} else {
					golog.Debug("Skipping document: has empty phoLH array", "_id", id)
					speedMeter.Check("skipped_empty_phoLH", 1)
				}
				return nil
			}

			// Check if it's an array (primitive.A or []interface{})
			if phoArray, ok := phoLH.(primitive.A); ok {
				return handleArraySkip(len(phoArray))
			} else if phoArray, ok := phoLH.([]interface{}); ok {
				return handleArraySkip(len(phoArray))
			} else {
				// phoLH exists and is not null, but is not an array - skip it anyway
				golog.Debug("Skipping document: has non-array phoLH", "_id", id, "phoLH_type", fmt.Sprintf("%T", phoLH))
				speedMeter.Check("skipped_non_array_phoLH", 1)
				return nil
			}
		}
	}
	// For specific ID processing or custom query, no phoLH check needed - process directly

	golog.Debug("Document needs to be added to queue", "_id", id)

	// Get priority for queue
	priority := getPriority(doc)
	if priority < 0 {
		golog.Error("invalid priority value", "priority", priority, "propId", id)
		speedMeter.Check("priorityErrors", 1)
		return fmt.Errorf("invalid priority value: %d", priority)
	}

	// Check if this is a dry run
	if *dryrunFlag {
		speedMeter.Check("dryrun", 1)
		golog.Info("Dry run mode: Would add to queue",
			"_id", id,
			"priority", priority)
		return nil
	}

	// Add to download queue
	err := queue.AddToQueue(id, priority, *boardFlag)
	if err != nil {
		golog.Error("Failed to add to queue",
			"_id", id,
			"board", *boardFlag,
			"error", err)
		speedMeter.Check("queueErrors", 1)
		return fmt.Errorf("failed to add to queue: %w", err)
	}

	// Track successful additions
	speedMeter.Check("added", 1)

	golog.Info("Successfully added to queue",
		"_id", id,
		"board", *boardFlag,
		"priority", priority)

	return nil
}

// getPriority calculates priority for a document
func getPriority(doc bson.M) int {
	// If processing specific IDs or custom query (from command line), always use priority 50000
	if *idFlag != "" || *queryFlag != "" {
		return 50000
	}

	// Use the goresodownload package's priority calculator for batch processing
	priority, err := goresodownload.CalculatePriority(*boardFlag, doc)
	if err != nil {
		golog.Debug("Failed to calculate priority, using default", "error", err)
		return 1000 // Default priority for batch processing
	}
	return priority
}

func main() {
	// Create context without timeout to allow long-running operations
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	golog.Info("Starting AddToQueue batch process")
	if err := processProperties(ctx); err != nil {
		golog.Fatal("Failed to process properties", "error", err)
	}
	golog.Info("AddToQueue batch process completed successfully")
}
