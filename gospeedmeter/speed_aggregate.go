package gospeedmeter

import (
	"fmt"
	"sort"
	"sync"
	"time"

	golog "github.com/real-rm/golog"
)

// DataPoint represents a single data point with timestamp and value
type DataPoint struct {
	Timestamp time.Time
	Value     float64
}

// SpeedAggregateOptions defines the configuration options for SpeedAggregate
type SpeedAggregateOptions struct {
	WindowDuration       time.Duration         // 滑动窗口时间长度，默认60秒
	CleanupInterval      time.Duration         // 清理过期数据的间隔，默认10秒
	IntervalCallback     func(*SpeedAggregate) // 定期回调函数
	IntervalTriggerCount int                   // 触发回调的数据点数量
	MinDataPoints        int                   // 计算速度所需的最小数据点数，默认2
}

// SpeedAggregate handles sliding window speed calculations for multiple metrics
type SpeedAggregate struct {
	dataPoints           map[string][]DataPoint // 每个指标的数据点列表
	mu                   sync.RWMutex           // 读写锁
	windowDuration       time.Duration          // 滑动窗口时间长度
	cleanupInterval      time.Duration          // 清理间隔
	intervalCallback     func(*SpeedAggregate)  // 回调函数
	intervalTriggerCount int                    // 触发回调的计数
	minDataPoints        int                    // 最小数据点数
	counter              int                    // 总计数器
	lastCleanup          time.Time              // 上次清理时间
	stopCleanup          chan struct{}          // 停止清理的信号
	cleanupRunning       bool                   // 清理是否正在运行
}

// NewSpeedAggregate creates a new SpeedAggregate instance
func NewSpeedAggregate(options SpeedAggregateOptions) *SpeedAggregate {
	// 设置默认值
	windowDuration := 60 * time.Second
	cleanupInterval := 10 * time.Second
	intervalTriggerCount := 100
	minDataPoints := 2

	if options.WindowDuration > 0 {
		windowDuration = options.WindowDuration
	}
	if options.CleanupInterval > 0 {
		cleanupInterval = options.CleanupInterval
	}
	if options.IntervalTriggerCount > 0 {
		intervalTriggerCount = options.IntervalTriggerCount
	}
	if options.MinDataPoints > 0 {
		minDataPoints = options.MinDataPoints
	}

	// 默认回调函数
	intervalCallback := func(sa *SpeedAggregate) {
		golog.Info(sa.ToString(UnitS, nil))
	}
	if options.IntervalCallback != nil {
		intervalCallback = options.IntervalCallback
	}

	sa := &SpeedAggregate{
		dataPoints:           make(map[string][]DataPoint),
		windowDuration:       windowDuration,
		cleanupInterval:      cleanupInterval,
		intervalCallback:     intervalCallback,
		intervalTriggerCount: intervalTriggerCount,
		minDataPoints:        minDataPoints,
		lastCleanup:          time.Now(),
		stopCleanup:          make(chan struct{}),
	}

	// 启动后台清理 goroutine
	sa.startCleanupRoutine()

	return sa
}

// AddDataPoint adds a new data point for the specified metric
func (sa *SpeedAggregate) AddDataPoint(name string, value float64) {
	now := time.Now()
	dataPoint := DataPoint{
		Timestamp: now,
		Value:     value,
	}

	sa.mu.Lock()
	sa.dataPoints[name] = append(sa.dataPoints[name], dataPoint)
	sa.counter++
	counter := sa.counter
	sa.mu.Unlock()

	// 触发回调
	if sa.intervalCallback != nil && counter%sa.intervalTriggerCount == 0 {
		sa.intervalCallback(sa)
	}

	// 检查是否需要清理
	if now.Sub(sa.lastCleanup) > sa.cleanupInterval {
		sa.cleanupOldData()
	}
}

// GetCurrentSpeed returns the current speed for a specific metric in the specified unit
func (sa *SpeedAggregate) GetCurrentSpeed(name string, unit Unit) float64 {
	sa.mu.RLock()
	dataPoints, exists := sa.dataPoints[name]
	sa.mu.RUnlock()

	if !exists || len(dataPoints) < sa.minDataPoints {
		return 0
	}

	return sa.calculateSpeed(dataPoints, unit)
}

// GetAllSpeeds returns the current speeds for all metrics in the specified unit
func (sa *SpeedAggregate) GetAllSpeeds(unit Unit) map[string]float64 {
	sa.mu.RLock()
	defer sa.mu.RUnlock()

	speeds := make(map[string]float64)
	for name, dataPoints := range sa.dataPoints {
		if len(dataPoints) >= sa.minDataPoints {
			speeds[name] = sa.calculateSpeed(dataPoints, unit)
		} else {
			speeds[name] = 0
		}
	}

	return speeds
}

// calculateSpeed calculates speed from data points within the window
func (sa *SpeedAggregate) calculateSpeed(dataPoints []DataPoint, unit Unit) float64 {
	if len(dataPoints) < sa.minDataPoints {
		return 0
	}

	now := time.Now()
	cutoffTime := now.Add(-sa.windowDuration)

	// 找到窗口内的数据点
	var windowPoints []DataPoint
	for _, point := range dataPoints {
		if point.Timestamp.After(cutoffTime) {
			windowPoints = append(windowPoints, point)
		}
	}

	if len(windowPoints) < sa.minDataPoints {
		return 0
	}

	// 计算窗口内的总值和时间跨度
	totalValue := 0.0
	for _, point := range windowPoints {
		totalValue += point.Value
	}

	// 使用窗口的实际时间跨度
	timeSpan := windowPoints[len(windowPoints)-1].Timestamp.Sub(windowPoints[0].Timestamp)
	if timeSpan == 0 {
		return 0
	}

	// 转换为指定单位
	denominator, ok := unitMap[unit]
	if !ok {
		denominator = unitMap[UnitS] // 默认使用秒
	}

	speedPerMs := totalValue / float64(timeSpan.Milliseconds())
	return speedPerMs * float64(denominator)
}

// cleanupOldData removes data points outside the window
func (sa *SpeedAggregate) cleanupOldData() {
	sa.mu.Lock()
	defer sa.mu.Unlock()

	now := time.Now()
	cutoffTime := now.Add(-sa.windowDuration)

	for name, dataPoints := range sa.dataPoints {
		// 找到第一个在窗口内的数据点
		validIndex := -1
		for i, point := range dataPoints {
			if point.Timestamp.After(cutoffTime) {
				validIndex = i
				break
			}
		}

		// 保留窗口内的数据点
		if validIndex == -1 {
			// 所有数据点都过期了，清空
			sa.dataPoints[name] = []DataPoint{}
		} else if validIndex > 0 {
			// 有部分数据点过期，保留有效的
			sa.dataPoints[name] = dataPoints[validIndex:]
		}
		// validIndex == 0 时，所有数据点都在窗口内，不需要清理
	}

	sa.lastCleanup = now
}

// startCleanupRoutine starts a background goroutine for periodic cleanup
func (sa *SpeedAggregate) startCleanupRoutine() {
	if sa.cleanupRunning {
		return
	}

	sa.cleanupRunning = true
	go func() {
		ticker := time.NewTicker(sa.cleanupInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				sa.cleanupOldData()
			case <-sa.stopCleanup:
				return
			}
		}
	}()
}

// Stop stops the background cleanup routine
func (sa *SpeedAggregate) Stop() {
	if sa.cleanupRunning {
		close(sa.stopCleanup)
		sa.cleanupRunning = false
	}
}

// ToString returns a formatted string representation of the speed aggregate
func (sa *SpeedAggregate) ToString(unit Unit, toBeEstimated map[string]float64) string {
	speeds := sa.GetAllSpeeds(unit)
	counters := sa.GetTotalCounters()

	// 按名称排序
	var names []string
	for name := range speeds {
		names = append(names, name)
	}
	sort.Strings(names)

	var result []string
	for _, name := range names {
		speed := speeds[name]
		counter := counters[name]
		speedStr := numberToShow(speed)
		counterStr := numberToShow(counter)

		toShow := fmt.Sprintf("%s(%s):%s/%s[sliding]", name, counterStr, speedStr, unit)

		if toBeEstimated != nil {
			if target, ok := toBeEstimated[name]; ok && speed > 0 {
				remaining := target - counter
				if remaining > 0 {
					estimate := remaining / speed
					toShow += fmt.Sprintf(" est:%s %s", numberToShow(estimate), unit)
				}
			}
		}
		result = append(result, toShow)
	}

	return fmt.Sprintf("%v", result)
}

// GetTotalCounters returns the total accumulated values for all metrics
func (sa *SpeedAggregate) GetTotalCounters() map[string]float64 {
	sa.mu.RLock()
	defer sa.mu.RUnlock()

	counters := make(map[string]float64)
	for name, dataPoints := range sa.dataPoints {
		total := 0.0
		for _, point := range dataPoints {
			total += point.Value
		}
		counters[name] = total
	}

	return counters
}

// GetDataPointCount returns the number of data points for each metric
func (sa *SpeedAggregate) GetDataPointCount() map[string]int {
	sa.mu.RLock()
	defer sa.mu.RUnlock()

	counts := make(map[string]int)
	for name, dataPoints := range sa.dataPoints {
		counts[name] = len(dataPoints)
	}

	return counts
}

// Reset clears all data points
func (sa *SpeedAggregate) Reset() {
	sa.mu.Lock()
	defer sa.mu.Unlock()

	sa.dataPoints = make(map[string][]DataPoint)
	sa.counter = 0
	sa.lastCleanup = time.Now()
}
