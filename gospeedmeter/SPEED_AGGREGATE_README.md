# SpeedAggregate - 滑动窗口速度聚合模块

## 概述

`SpeedAggregate` 是 `gospeedmeter` 包中的新模块，专门用于基于滑动窗口的瞬时速度计算。与现有的 `SpeedMeter`（计算累计平均速度）不同，`SpeedAggregate` 提供了更精确的实时速度监控能力。

## 核心特性

- **滑动窗口算法**：基于时间窗口计算瞬时速度，而非累计平均
- **多指标支持**：同时跟踪多个性能指标
- **线程安全**：支持多 goroutine 并发访问
- **自动清理**：后台自动清理过期数据点
- **灵活配置**：可配置窗口大小、清理间隔等参数
- **回调支持**：支持定期回调函数进行监控

## 与 SpeedMeter 的区别

| 特性 | SpeedMeter | SpeedAggregate |
|------|------------|----------------|
| 计算方式 | 累计平均速度 | 滑动窗口瞬时速度 |
| 数据存储 | 总累计值 + 开始时间 | 时间序列数据点 |
| 内存使用 | 低（固定） | 中等（随窗口大小变化） |
| 速度响应 | 慢（历史影响大） | 快（只考虑近期数据） |
| 适用场景 | 长期趋势监控 | 实时性能监控 |

## 基本使用

### 创建 SpeedAggregate

```go
package main

import (
    "time"
    "github.com/real-rm/gospeedmeter"
)

func main() {
    // 使用默认配置
    sa := gospeedmeter.NewSpeedAggregate(gospeedmeter.SpeedAggregateOptions{})
    defer sa.Stop() // 重要：停止后台清理 goroutine

    // 或使用自定义配置
    sa = gospeedmeter.NewSpeedAggregate(gospeedmeter.SpeedAggregateOptions{
        WindowDuration:       60 * time.Second, // 60秒滑动窗口
        CleanupInterval:      10 * time.Second, // 每10秒清理过期数据
        IntervalTriggerCount: 100,              // 每100个数据点触发回调
        MinDataPoints:        2,                // 至少2个数据点才计算速度
        IntervalCallback: func(sa *gospeedmeter.SpeedAggregate) {
            fmt.Println("Current speeds:", sa.ToString(gospeedmeter.UnitS, nil))
        },
    })
    defer sa.Stop()
}
```

### 添加数据点

```go
// 记录下载字节数
sa.AddDataPoint("downloadBytes", 1024)

// 记录写入字节数
sa.AddDataPoint("writeBytes", 512)

// 记录下载文件数
sa.AddDataPoint("downloadCount", 1)
```

### 获取速度

```go
// 获取单个指标的当前速度
downloadSpeed := sa.GetCurrentSpeed("downloadBytes", gospeedmeter.UnitS)
fmt.Printf("Download speed: %.2f bytes/s\n", downloadSpeed)

// 获取所有指标的当前速度
allSpeeds := sa.GetAllSpeeds(gospeedmeter.UnitS)
for name, speed := range allSpeeds {
    fmt.Printf("%s: %.2f/s\n", name, speed)
}
```

## 在 goresodownload 中的集成方案

### 1. 修改 DownloaderOptions

```go
type DownloaderOptions struct {
    Config         *DownloaderConfig
    StoragePaths   []string
    MergedCol      *gomongo.MongoCollection
    FailedCol      *gomongo.MongoCollection
    SpeedMeter     *gospeedmeter.SpeedMeter     // 保留原有的累计速度
    SpeedAggregate *gospeedmeter.SpeedAggregate // 新增滑动窗口速度
}
```

### 2. 在下载过程中记录数据

```go
// 在 downloadAndCacheFiles 方法中
func (pp *PropertyProcessor) downloadAndCacheFiles() error {
    // ... 现有代码 ...
    
    for job := range taskChan {
        // 下载文件
        fileData, err := gofile.DownloadAndReadWithRetry(url, maxRetries)
        if err == nil {
            // 记录到两个速度计算器
            if pp.downloader.SpeedMeter != nil {
                pp.downloader.SpeedMeter.Check("downloadBytes", float64(len(fileData)))
                pp.downloader.SpeedMeter.Check("downloadMedia", 1)
            }
            
            if pp.downloader.SpeedAggregate != nil {
                pp.downloader.SpeedAggregate.AddDataPoint("downloadBytes", float64(len(fileData)))
                pp.downloader.SpeedAggregate.AddDataPoint("downloadMedia", 1)
            }
        }
    }
}
```

### 3. 在写入过程中记录数据

```go
// 在 batchWriteFiles 方法中
func (pp *PropertyProcessor) batchWriteFiles() error {
    // ... 现有代码 ...
    
    for _, basePath := range pp.downloader.StoragePaths {
        for _, cachedFile := range pp.cachedFiles {
            // 写入文件
            err := os.WriteFile(fullPath, cachedFile.Data, 0644)
            if err == nil {
                // 记录写入速度
                if pp.downloader.SpeedMeter != nil {
                    diskKey := fmt.Sprintf("writeBytes_%s", basePath)
                    pp.downloader.SpeedMeter.Check(diskKey, float64(len(cachedFile.Data)))
                }
                
                if pp.downloader.SpeedAggregate != nil {
                    diskKey := fmt.Sprintf("writeBytes_%s", basePath)
                    pp.downloader.SpeedAggregate.AddDataPoint(diskKey, float64(len(cachedFile.Data)))
                }
            }
        }
    }
}
```

### 4. 创建下载器时初始化

```go
// 在 createDownloader 函数中
func createDownloader() (*goresodownload.Downloader, error) {
    // 创建滑动窗口速度聚合器
    speedAggregate := gospeedmeter.NewSpeedAggregate(gospeedmeter.SpeedAggregateOptions{
        WindowDuration:       60 * time.Second, // 1分钟滑动窗口
        CleanupInterval:      15 * time.Second, // 每15秒清理
        IntervalTriggerCount: 50,               // 每50个数据点打印
        IntervalCallback: func(sa *gospeedmeter.SpeedAggregate) {
            golog.Info("Sliding window speeds: " + sa.ToString(gospeedmeter.UnitS, nil))
        },
    })

    opts := &goresodownload.DownloaderOptions{
        Config:         goresodownload.NewDefaultConfig(),
        StoragePaths:   storagePaths,
        MergedCol:      gomongo.Coll("rni", goresodownload.BoardMergedTable[gBoardType]),
        FailedCol:      FailedCol,
        SpeedMeter:     speedMeter,     // 保留原有的
        SpeedAggregate: speedAggregate, // 新增的
    }

    return goresodownload.NewDownloader(opts)
}
```

## 配置参数说明

- **WindowDuration**: 滑动窗口的时间长度，建议 30-120 秒
- **CleanupInterval**: 清理过期数据的间隔，建议为窗口时间的 1/4 到 1/6
- **IntervalTriggerCount**: 触发回调的数据点数量
- **MinDataPoints**: 计算速度所需的最小数据点数，至少为 2

## 性能考虑

1. **内存使用**: 每个数据点约占用 24 字节（时间戳 + 数值），1分钟窗口通常包含几百到几千个数据点
2. **CPU 开销**: 每次添加数据点时需要检查是否清理，清理操作的复杂度为 O(n)
3. **并发性能**: 使用读写锁优化并发访问性能

## 最佳实践

1. **合理设置窗口大小**: 根据业务需求平衡响应速度和稳定性
2. **及时停止**: 程序退出时调用 `Stop()` 方法停止后台 goroutine
3. **监控内存**: 在高频数据场景下监控内存使用情况
4. **组合使用**: 与 SpeedMeter 组合使用，获得全面的速度监控

## 示例输出

```
Sliding window speeds: [downloadBytes(2.5M):1.2M/s[sliding] writeBytes_/disk1(2.0M):980K/s[sliding] writeBytes_/disk2(2.0M):1.1M/s[sliding]]
```

输出格式：`指标名(总累计值):瞬时速度/单位[sliding]`
